import 'package:example/view/common/InformationalScreen.dart';
import 'package:flutter/cupertino.dart';
import 'package:digital_onboarding/digital_onboarding.dart';
import '../../resources/exports/index.dart';

enum EnumDocumentTypeSelection { non, id, passport }

class DocumentTypeSelectionWidget extends StatefulWidget {
  final Function(DocumentTypeSelection) onDocumentTypeSelected;

  const DocumentTypeSelectionWidget({
    super.key,
    required this.onDocumentTypeSelected,
  });

  @override
  State<DocumentTypeSelectionWidget> createState() => _DocumentTypeSelectionWidgetState();
}

class _DocumentTypeSelectionWidgetState extends State<DocumentTypeSelectionWidget> {
  EnumDocumentTypeSelection _selectedDocumentType = EnumDocumentTypeSelection.non;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: "Document Verification",
        backallow: true,
      ),
      backgroundColor: AppColors.white,
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Spacer(),
            SvgPicture.asset("assets/images/DocumentScan2.svg"),
            const SpaceH20(),
            // Title
            const Text(
              "Please choose a document to scan",
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontWeight: FontWeight.w700,
              ),
            ),

            const Spacer(),
            // National ID Button
            _buildDocumentTypeButton(
              context: context,
              title: "National ID",
              documentType: EnumDocumentTypeSelection.id,
              onPressed: () => _selectDocumentType(EnumDocumentTypeSelection.id),
            ),
            const SpaceH16(),
            // Passport Button
            _buildDocumentTypeButton(
              context: context,
              title: "Passport",
              documentType: EnumDocumentTypeSelection.passport,
              onPressed: () => _selectDocumentType(EnumDocumentTypeSelection.passport),
            ),

            const Spacer(),
            // Proceed Button
            CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: _selectedDocumentType != EnumDocumentTypeSelection.non
                  ? () {
                      if (_selectedDocumentType == EnumDocumentTypeSelection.id) {
                        Get.to(Informationalscreen(imageOption: InformationalImageOptions.scanDocument, title: "Scan Document Front", onSubmit: () => _openCamera('national')));
                      } else if (_selectedDocumentType == EnumDocumentTypeSelection.passport) {
                        Get.to(Informationalscreen(imageOption: InformationalImageOptions.scanDocument, title: "Scan Passport Main Page", onSubmit: () => _openCamera('passport')));
                      }
                    }
                  : null,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 14),
                width: double.maxFinite,
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  color: _selectedDocumentType != EnumDocumentTypeSelection.non ? const Color(0xff8240DE) : const Color(0xff8240DE).withOpacity(0.5),
                  shape: ContinuousRectangleBorder(
                    borderRadius: BorderRadius.circular(24.0),
                  ),
                ),
                alignment: Alignment.center,
                child: DefaultTextStyle(
                  style: TextStyle(
                    color: _selectedDocumentType != EnumDocumentTypeSelection.non ? Colors.white : Colors.white.withOpacity(0.7),
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  child: const Text("Proceed"),
                ),
              ),
            ),

            const Spacer(),
          ],
        ),
      ),
    );
  }

  void _selectDocumentType(EnumDocumentTypeSelection documentType) {
    setState(() {
      _selectedDocumentType = documentType;
    });
  }

  void _openCamera(String cardType) async {
    print("🔍 DEBUG: _openCamera called with cardType: $cardType");

    // Use the existing ImagePickerService to open camera with document frame
    Uint8List? image = await ImagePickerService.openFrame(cardType);

    print("🔍 DEBUG: Image captured from camera");
    print("🔍 DEBUG: Image is null: ${image == null}");
    if (image != null) {
      print("🔍 DEBUG: Image size: ${image.length} bytes");
    }

    if (image != null) {
      // Show confirmation dialog with captured image
      _showConfirmationDialog(image, cardType);
    } else {
      print("🔍 DEBUG: No image captured, not showing dialog");
    }
  }

  void _showConfirmationDialog(Uint8List image, String cardType) {
    String title = cardType == 'national' ? 'Iraqi National ID Front' : 'Passport Front';

    // TODO: Dialog
    CustomDialog.showDialog(
      title: title,
      content: Container(
        color: AppColors.white,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ImageService.image(
                image,
                borderRadius: 12.0,
                imageHeight: 200,
                imageWidth: 300,
                fit: BoxFit.cover,
              ),
            ],
          ),
        ),
      ),
      onSave: () => _saveAndVerify(image, cardType),
      saveButtonText: "Save",
    );
  }

  void _saveAndVerify(Uint8List image, String cardType) async {
    print("🔍 DEBUG: _saveAndVerify method called!");
    print("🔍 DEBUG: Image size: ${image.length} bytes");
    print("🔍 DEBUG: Card type: $cardType");

    Get.back(); // Close dialog

    // Show loading
    GlobalHelper.showPopupLoader();

    try {
      // Convert to XFile for verification
      final XFile file = await GlobalHelper().getFileFromUint8List(image, "doc");

      print("🔍 DEBUG: Starting document verification...");
      print("🔍 DEBUG: Card type: $cardType");
      print("🔍 DEBUG: File path: ${file.path}");
      print("🔍 DEBUG: File size: ${await file.length()} bytes");

      // Submit document for verification
      final VerifyDocModel? model = await DigitalOnboardingServices.submitDocument(
        file,
        step: StepEnum.documentFront,
      );

      print("🔍 DEBUG: API Response received");
      print("🔍 DEBUG: Model is null: ${model == null}");
      if (model != null) {
        print("🔍 DEBUG: model.verified: ${model.verified}");
        print("🔍 DEBUG: model.isBackRequired: ${model.isBackRequired}");
        print("🔍 DEBUG: Full model: $model");
      }

      Get.back(); // Close loading

      if (model?.verified ?? false) {
        print("🔍 DEBUG: Document verified successfully");
        if (model?.isBackRequired ?? false) {
          print("🔍 DEBUG: Back side required, showing back informational screen");
          // Need back side - show informational screen for back
          _showBackSideInformational(cardType);
        } else {
          print("🔍 DEBUG: Verification complete, no back side needed");
          // Verification complete
          CustomSnackBar.successSnackBar(message: "Document verification completed!");
          Get.offAllNamed(Routes.DASHBOARD);
        }
      } else {
        print("🔍 DEBUG: Document verification failed");
        CustomSnackBar.errorSnackBar(message: "Document verification failed. Please try again.");
      }
    } on DigitalOnboardingException catch (e) {
      print("🔍 DEBUG: DigitalOnboardingException caught");
      print("🔍 DEBUG: Exception message: ${e.message}");
      print("🔍 DEBUG: Exception toString: $e");
      Get.back(); // Close loading
      CustomSnackBar.errorSnackBar(message: e.message ?? Strings.SOMETHING_WENT_WRONG);
    } catch (e) {
      print("🔍 DEBUG: General exception caught");
      print("🔍 DEBUG: Exception: $e");
      Get.back(); // Close loading
      CustomSnackBar.errorSnackBar(message: "Unexpected error: $e");
    }
  }

  void _showBackSideInformational(String cardType) {
    String title = cardType == 'national' ? 'Scan Document Back' : 'Scan Passport Back';

    Get.to(Informationalscreen(
      imageOption: InformationalImageOptions.scanBack,
      title: title,
      onSubmit: () => _openBackCamera(cardType),
    ));
  }

  void _openBackCamera(String cardType) async {
    // Use the existing ImagePickerService to open camera for back side
    Uint8List? image = await ImagePickerService.openFrame(cardType);

    if (image != null) {
      // Show confirmation dialog for back side
      _showBackConfirmationDialog(image, cardType);
    }
  }

  void _showBackConfirmationDialog(Uint8List image, String cardType) {
    String title = cardType == 'national' ? 'Iraqi National ID Back' : 'Passport Back';

    CustomDialog.showDialog(
      title: title,
      content: Container(
        color: AppColors.white,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ImageService.image(
                image,
                borderRadius: 12.0,
                imageHeight: 200,
                imageWidth: 300,
                fit: BoxFit.cover,
              ),
            ],
          ),
        ),
      ),
      onSave: () => _saveBackAndVerify(image),
      saveButtonText: "Save",
    );
  }

  void _saveBackAndVerify(Uint8List image) async {
    Get.back(); // Close dialog

    // Show loading
    GlobalHelper.showPopupLoader();

    try {
      // Convert to XFile for verification
      final XFile file = await GlobalHelper().getFileFromUint8List(image, "doc");

      print("🔍 DEBUG: Starting BACK side document verification...");
      print("🔍 DEBUG: File path: ${file.path}");
      print("🔍 DEBUG: File size: ${await file.length()} bytes");

      // Submit back document for verification
      final VerifyDocModel? model = await DigitalOnboardingServices.submitDocument(
        file,
        step: StepEnum.documentBack,
      );

      print("🔍 DEBUG: BACK side API Response received");
      print("🔍 DEBUG: Model is null: ${model == null}");
      if (model != null) {
        print("🔍 DEBUG: model.verified: ${model.verified}");
        print("🔍 DEBUG: Full back model: $model");
      }

      Get.back(); // Close loading

      if (model?.verified ?? false) {
        print("🔍 DEBUG: Back side verification successful");
        // Verification complete
        CustomSnackBar.successSnackBar(message: "Document verification completed!");
        Get.offAllNamed(Routes.DASHBOARD);
      } else {
        print("🔍 DEBUG: Back side verification failed");
        CustomSnackBar.errorSnackBar(message: "Back side verification failed. Please try again.");
      }
    } on DigitalOnboardingException catch (e) {
      print("🔍 DEBUG: BACK side DigitalOnboardingException caught");
      print("🔍 DEBUG: Exception message: ${e.message}");
      print("🔍 DEBUG: Exception toString: $e");
      Get.back(); // Close loading
      CustomSnackBar.errorSnackBar(message: e.message ?? Strings.SOMETHING_WENT_WRONG);
    } catch (e) {
      print("🔍 DEBUG: BACK side general exception caught");
      print("🔍 DEBUG: Exception: $e");
      Get.back(); // Close loading
      CustomSnackBar.errorSnackBar(message: "Unexpected error: $e");
    }
  }

  Widget _buildDocumentTypeButton({
    required BuildContext context,
    required String title,
    required EnumDocumentTypeSelection documentType,
    required VoidCallback onPressed,
  }) {
    bool isSelected = _selectedDocumentType == documentType;

    return CupertinoButton(
      onPressed: onPressed,
      padding: EdgeInsets.zero,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: ShapeDecoration(
          color: isSelected ? const Color(0xff8240DE).withOpacity(0.1) : const Color(0xffE8E0EB),
          shape: ContinuousRectangleBorder(
            borderRadius: BorderRadius.circular(24.0),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Radio<EnumDocumentTypeSelection>(
                value: documentType,
                groupValue: _selectedDocumentType,
                onChanged: (EnumDocumentTypeSelection? value) {
                  if (value != null) {
                    _selectDocumentType(value);
                  }
                },
                activeColor: const Color(0xff8240DE),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: context.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                  color: isSelected ? const Color(0xff8240DE) : Colors.black,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
