import 'package:example/view/common/InformationalScreen.dart';
import 'package:flutter/cupertino.dart';
import '../../resources/exports/index.dart';

class DocVerification extends GetView<DocVerificationController> {
  const DocVerification({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: controller.currentStepTitle,
        backallow: true,
      ),
      backgroundColor: AppColors.white,
      body: GetBuilder<DocVerificationController>(
        id: 'verification_step',
        builder: (_) {
          switch (controller.currentStep) {
            case VerificationStep.documentSelection:
              return _buildDocumentSelection();
            case VerificationStep.frontInformational:
              return _buildInformationalScreen(isBack: false);
            case VerificationStep.frontConfirmation:
              return _buildConfirmationScreen(isBack: false);
            case VerificationStep.backInformational:
              return _buildInformationalScreen(isBack: true);
            case VerificationStep.backConfirmation:
              return _buildConfirmationScreen(isBack: true);
            case VerificationStep.completed:
              return _buildCompletedScreen();
            default:
              return _buildDocumentSelection();
          }
        },
      ),
    );
  }

  Widget _buildDocumentSelection() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const Spacer(),
          SvgPicture.asset("assets/images/DocumentScan2.svg"),
          const SpaceH20(),
          const Text(
            "Please choose a document to scan",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.black,
              fontSize: 16,
              fontWeight: FontWeight.w700,
            ),
          ),
          const Spacer(),
          GetBuilder<DocVerificationController>(
            id: 'document_selection',
            builder: (_) => Column(
              children: [
                _buildDocumentTypeButton(
                  title: "National ID",
                  documentType: DocumentTypeSelection.nationalId,
                  onPressed: () => controller.selectDocumentType(DocumentTypeSelection.nationalId),
                ),
                const SpaceH16(),
                _buildDocumentTypeButton(
                  title: "Passport",
                  documentType: DocumentTypeSelection.passport,
                  onPressed: () => controller.selectDocumentType(DocumentTypeSelection.passport),
                ),
              ],
            ),
          ),
          const Spacer(),
          GetBuilder<DocVerificationController>(
            id: 'document_selection',
            builder: (_) => CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: controller.canProceed ? () => controller.startVerification() : null,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 14),
                width: double.maxFinite,
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  color: controller.canProceed 
                      ? const Color(0xff8240DE) 
                      : const Color(0xff8240DE).withOpacity(0.5),
                  shape: ContinuousRectangleBorder(
                    borderRadius: BorderRadius.circular(24.0),
                  ),
                ),
                alignment: Alignment.center,
                child: DefaultTextStyle(
                  style: TextStyle(
                    color: controller.canProceed 
                        ? Colors.white 
                        : Colors.white.withOpacity(0.7),
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  child: const Text("Proceed"),
                ),
              ),
            ),
          ),
          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildInformationalScreen({required bool isBack}) {
    String title = isBack 
        ? (controller.selectedDocumentType == DocumentTypeSelection.nationalId 
            ? "Scan Document Back" 
            : "Scan Passport Back")
        : (controller.selectedDocumentType == DocumentTypeSelection.nationalId 
            ? "Scan Document Front" 
            : "Scan Passport Main Page");

    return Informationalscreen(
      imageOption: isBack 
          ? InformationalImageOptions.scanBack 
          : InformationalImageOptions.scanDocument,
      title: title,
      onSubmit: () => controller.openCamera(),
    );
  }

  Widget _buildConfirmationScreen({required bool isBack}) {
    String title = isBack 
        ? (controller.selectedDocumentType == DocumentTypeSelection.nationalId 
            ? 'Iraqi National ID Back' 
            : 'Passport Back')
        : (controller.selectedDocumentType == DocumentTypeSelection.nationalId 
            ? 'Iraqi National ID Front' 
            : 'Passport Front');

    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        children: [
          const Spacer(),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SpaceH20(),
          GetBuilder<DocVerificationController>(
            id: 'doc_picture',
            builder: (_) => controller.image != null
                ? Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: ImageService.image(
                        controller.image!,
                        borderRadius: 12.0,
                        imageHeight: 200,
                        imageWidth: 300,
                        fit: BoxFit.cover,
                      ),
                    ),
                  )
                : Container(
                    height: 200,
                    width: 300,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: Colors.grey.shade200,
                    ),
                    child: const Center(
                      child: Text("No image captured"),
                    ),
                  ),
          ),
          const Spacer(),
          Row(
            children: [
              Expanded(
                child: CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () => controller.retryCapture(),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    decoration: ShapeDecoration(
                      color: Colors.grey.shade300,
                      shape: ContinuousRectangleBorder(
                        borderRadius: BorderRadius.circular(24.0),
                      ),
                    ),
                    alignment: Alignment.center,
                    child: const Text(
                      "Retry",
                      style: TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ),
              const SpaceW16(),
              Expanded(
                child: Obx(() => CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: controller.isLoading.value 
                      ? null 
                      : () => controller.confirmAndVerify(),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    decoration: ShapeDecoration(
                      color: controller.isLoading.value 
                          ? const Color(0xff8240DE).withOpacity(0.5)
                          : const Color(0xff8240DE),
                      shape: ContinuousRectangleBorder(
                        borderRadius: BorderRadius.circular(24.0),
                      ),
                    ),
                    alignment: Alignment.center,
                    child: controller.isLoading.value
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : const Text(
                            "Save",
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                  ),
                )),
              ),
            ],
          ),
          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildCompletedScreen() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 80,
          ),
          SpaceH20(),
          Text(
            "Document Verification Completed!",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentTypeButton({
    required String title,
    required DocumentTypeSelection documentType,
    required VoidCallback onPressed,
  }) {
    bool isSelected = controller.selectedDocumentType == documentType;

    return CupertinoButton(
      onPressed: onPressed,
      padding: EdgeInsets.zero,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: ShapeDecoration(
          color: isSelected 
              ? const Color(0xff8240DE).withOpacity(0.1) 
              : const Color(0xffE8E0EB),
          shape: ContinuousRectangleBorder(
            borderRadius: BorderRadius.circular(24.0),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Radio<DocumentTypeSelection>(
                value: documentType,
                groupValue: controller.selectedDocumentType,
                onChanged: (DocumentTypeSelection? value) {
                  if (value != null) {
                    controller.selectDocumentType(value);
                  }
                },
                activeColor: const Color(0xff8240DE),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                  color: isSelected ? const Color(0xff8240DE) : Colors.black,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
