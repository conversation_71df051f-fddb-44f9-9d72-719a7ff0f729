import '../../resources/exports/index.dart';
import '../common/InformationalScreen.dart';
import 'document_type_selection.dart' as doc_selection;
import 'document_camera_capture.dart';

class DocVerification extends GetView<DocVerificationController> {
  const DocVerification({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<DocVerificationController>(
      id: 'verification_step',
      builder: (_) {
        switch (controller.currentStep) {
          case VerificationStep.documentSelection:
            return doc_selection.DocumentTypeSelectionWidget(
              onDocumentTypeSelected: controller.selectDocumentType,
            );

          case VerificationStep.frontInformational:
            return _buildInformationalScreen(
              context: context,
              title: "Scan Document Front",
              imageOption: InformationalImageOptions.scanFront,
              orderSubtitleList: const [
                "Hold your device steady",
                "Make sure the document is well lit",
                "Position the document within the frame",
                "Ensure all text is clearly visible",
              ],
              onSubmit: controller.proceedToFrontCapture,
            );

          case VerificationStep.frontCapture:
            return DocumentCameraCapture(
              documentType: controller.selectedDocumentType == DocumentTypeSelection.nationalId ? "National ID" : "Passport",
              side: "front",
              onCapture: controller.captureDocument,
            );

          case VerificationStep.frontConfirmation:
            return _buildConfirmationDialog(
              context: context,
              title: controller.selectedDocumentType == DocumentTypeSelection.nationalId ? "Iraqi National ID Front" : "Passport Front",
              onRetry: controller.retryCapture,
              onSave: controller.saveAndVerifyDocument,
            );

          case VerificationStep.backInformational:
            return _buildInformationalScreen(
              context: context,
              title: "Scan Document Back",
              imageOption: InformationalImageOptions.scanBack,
              orderSubtitleList: const [
                "Hold your device steady",
                "Make sure the document is well lit",
                "Position the document within the frame",
                "Ensure all text is clearly visible",
              ],
              onSubmit: controller.proceedToBackCapture,
            );

          case VerificationStep.backCapture:
            return DocumentCameraCapture(
              documentType: controller.selectedDocumentType == DocumentTypeSelection.nationalId ? "National ID" : "Passport",
              side: "back",
              onCapture: controller.captureDocument,
            );

          case VerificationStep.backConfirmation:
            return _buildConfirmationDialog(
              context: context,
              title: controller.selectedDocumentType == DocumentTypeSelection.nationalId ? "Iraqi National ID Back" : "Passport Back",
              onRetry: controller.retryCapture,
              onSave: controller.saveAndVerifyDocument,
            );
          case VerificationStep.completed:
            return _buildCompletedScreen(context);
        }
      },
    );
  }

  Widget _buildInformationalScreen({
    required BuildContext context,
    required String title,
    required InformationalImageOptions imageOption,
    required List<String> orderSubtitleList,
    required VoidCallback onSubmit,
  }) {
    return Informationalscreen(
      imageOption: imageOption,
      title: title,
      orderSubtitleList: orderSubtitleList,
      onSubmit: onSubmit,
    );
  }

  Widget _buildConfirmationDialog({
    required BuildContext context,
    required String title,
    required VoidCallback onRetry,
    required VoidCallback onSave,
  }) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: "Confirm Document",
        backallow: true,
      ),
      backgroundColor: AppColors.white,
      body: GetBuilder<DocVerificationController>(
        id: 'doc_picture',
        builder: (_) {
          if (controller.image == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return Column(
            children: [
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        title,
                        style: context.titleLarge.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SpaceH20(),
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 20),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: ImageService.image(
                            controller.image!,
                            borderRadius: 12.0,
                            imageHeight: 250,
                            imageWidth: 350,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Expanded(
                      child: CustomButton.solid(
                        backgroundColor: Colors.grey[300]!,
                        textColor: Colors.black,
                        text: "Retry",
                        onTap: onRetry,
                        radius: Sizes.RADIUS_12,
                        constraints: const BoxConstraints(minHeight: 55),
                      ),
                    ),
                    const SpaceW16(),
                    Expanded(
                      child: CustomButton.solid(
                        backgroundColor: AppColors.primary,
                        textColor: AppColors.white,
                        text: "Save",
                        onTap: onSave,
                        radius: Sizes.RADIUS_12,
                        constraints: const BoxConstraints(minHeight: 55),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildCompletedScreen(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: "Verification Complete",
        backallow: false,
      ),
      backgroundColor: AppColors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 80,
            ),
            const SpaceH20(),
            Text(
              "Document Verification Completed!",
              style: context.titleLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.green,
              ),
              textAlign: TextAlign.center,
            ),
            const SpaceH40(),
            CustomButton.solid(
              backgroundColor: AppColors.primary,
              textColor: AppColors.white,
              text: "Continue",
              onTap: () => Get.offAllNamed(Routes.DASHBOARD),
              radius: Sizes.RADIUS_12,
              constraints: const BoxConstraints(minHeight: 55),
            ).constrainedBox(maxWidth: 300.0),
          ],
        ),
      ),
    );
  }
}
