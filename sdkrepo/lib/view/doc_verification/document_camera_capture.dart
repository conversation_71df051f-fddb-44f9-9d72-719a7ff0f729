import '../../resources/exports/index.dart';

class DocumentCameraCapture extends StatelessWidget {
  final String documentType;
  final String side; // "front" or "back"
  final VoidCallback onCapture;

  const DocumentCameraCapture({
    super.key,
    required this.documentType,
    required this.side,
    required this.onCapture,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "Scan Document $side",
        backallow: true,
      ),
      backgroundColor: AppColors.black,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Spacer(),
            
            // Instructions
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                "Position your $documentType $side side within the frame and tap capture",
                style: context.bodyLarge.copyWith(
                  color: Colors.white,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            
            const SpaceH40(),
            
            // Camera placeholder (the actual camera will be handled by ImagePickerService.openFrame)
            Container(
              width: 300,
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppColors.primary,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.camera_alt,
                      color: AppColors.primary,
                      size: 60,
                    ),
                    const SpaceH16(),
                    Text(
                      "Camera View",
                      style: context.bodyLarge.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const Spacer(),
            
            // Capture Button
            Container(
              margin: const EdgeInsets.all(20),
              child: CustomButton.solid(
                backgroundColor: AppColors.primary,
                textColor: AppColors.white,
                text: "Capture Document",
                onTap: onCapture,
                radius: Sizes.RADIUS_12,
                constraints: const BoxConstraints(minHeight: 55),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
