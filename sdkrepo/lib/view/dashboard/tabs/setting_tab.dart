import 'package:example/resources/exports/index.dart';
import 'package:flutter/cupertino.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class SettingTab extends StatelessWidget {
  const SettingTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF9FDFF),
      appBar: AppBar(
        title: const Text("Setting"),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: [
              const SpaceH32(),
              CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () async {
                  final SharedPreferences prefs = await SharedPreferences.getInstance();
                  await prefs.remove("USER_NAME");
                  await prefs.remove("PASSWORD");

                  Get.offNamed(Routes.MERCHANT_LOGIN);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 15),
                  width: double.maxFinite,
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    color: const Color(0xffE8E0EB),
                    shape: ContinuousRectangleBorder(
                      borderRadius: BorderRadius.circular(24.0),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      DefaultTextStyle(
                        style: TextStyle(
                          color: Color(0xff4A454E),
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        child: Text("Logout"),
                      ),
                      Icon(
                        // Icons.logout_outlined,
                        CupertinoIcons.square_arrow_left,
                        color: Color(0xff4A454E),
                        size: 24,
                      ),
                    ],
                  ),
                ),
              ),
              const SpaceH16(),
              CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () async {
                  await _resetSession();
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 15),
                  width: double.maxFinite,
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    color: const Color(0xffE8E0EB),
                    shape: ContinuousRectangleBorder(
                      borderRadius: BorderRadius.circular(24.0),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      DefaultTextStyle(
                        style: TextStyle(
                          color: Color(0xff4A454E),
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        child: Text("Reset Session"),
                      ),
                      Icon(
                        CupertinoIcons.restart,
                        color: Color(0xff4A454E),
                        size: 24,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _resetSession() async {
    try {
      // Show loading indicator
      GlobalHelper.showPopupLoader();

      // Get the stored token and app key
      final String? token = AuthManager.instance.getToken();
      final String appKey = FlutterConfig.get("appId") ?? "";

      if (token == null || token.isEmpty) {
        Get.back(); // Close loading
        CustomSnackBar.errorSnackBar(message: "No authentication token found");
        return;
      }

      // Prepare headers
      Map<String, String> headers = {
        'Accept-Language': 'en',
        'Key': appKey,
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      // Make the API call
      final response = await http
          .post(
            Uri.parse('http://localhost:8000/api/v1/reset'),
            headers: headers,
          )
          .timeout(const Duration(seconds: 30));

      Get.back(); // Close loading

      if (response.statusCode == 200) {
        // Success - clear local session data and navigate
        await AuthManager.instance.logout(shouldNavigate: false);

        // Clear shared preferences
        final SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.remove("USER_NAME");
        await prefs.remove("PASSWORD");

        CustomSnackBar.successSnackBar(message: "Session reset successfully");
        Get.offAllNamed(Routes.MERCHANT_LOGIN);
      } else {
        // Handle error response
        String errorMessage = "Failed to reset session";
        try {
          final responseBody = jsonDecode(response.body);
          errorMessage = responseBody['message'] ?? errorMessage;
        } catch (e) {
          // Use default error message if JSON parsing fails
        }
        CustomSnackBar.errorSnackBar(message: errorMessage);
      }
    } catch (e) {
      Get.back(); // Close loading if still open
      CustomSnackBar.errorSnackBar(message: "Network error: ${e.toString()}");
    }
  }
}
