import 'package:mrz_scanner/mrz_scanner.dart';

import '../../../resources/exports/index.dart';
import 'package:path/path.dart' as p;

// import 'package:mrz_scanner/mrz_scanner.dart';
import 'package:image/image.dart' as img;

class ImagePickerService {
  static Future<XFile?> pickImage({
    ImageSource imageSource = ImageSource.gallery,
    double? maxWidth,
    double? maxHeight,
    int? quality,
  }) async {
    try {
      final XFile? image = await ImagePicker().pickImage(
        source: imageSource,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        imageQuality: quality,
      );

      if (image != null) {
        String ext = p.extension(image.path);
        log.w(ext);
        if (image.mimeType == 'image/jpeg' || image.mimeType == 'image/jpg' || image.mimeType == 'image/png' || ext == ".jpg" || ext == ".jpeg" || ext == ".png") {
          return image;
        } else {
          CustomSnackBar.toast(message: 'Invalid File Format.');
        }
      }
    } on PlatformException catch (_) {
      CustomSnackBar.errorToast(message: "Failed to pick Image.");
    }
    return null;
  }

  static Future<XFile?> showImagePickerSheet(BuildContext context) async {
    XFile? image;

    image = await CustomSnackBar.showCustomBottomSheet<XFile?>(
      color: context.scaffoldBackgroundColor,
      showDivider: false,
      maxHeight: 160,
      maxWidth: 500,
      bottomSheet: _buildImagePickerContent(),
    );

    return image;
  }

  static Widget _buildImagePickerContent() {
    XFile? image;

    return Material(
      type: MaterialType.transparency,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            onTap: () async {
              image = await pickImage(imageSource: ImageSource.camera);
              Get.back<XFile>(result: image);
            },
            leading: const Icon(
              Icons.camera_alt_outlined,
              color: AppColors.primary,
            ),
            title: Text(Strings.FROM_CAMERA, style: Get.context!.bodyLarge),
          ),
          ListTile(
            onTap: () async {
              image = await pickImage(imageSource: ImageSource.gallery);
              Get.back<XFile>(result: image);
            },
            leading: const Icon(Icons.image_outlined, color: AppColors.primary),
            title: Text(Strings.FROM_GALLERY, style: Get.context!.bodyLarge),
          ),
        ],
      ),
    );
  }

  static String filePath(MediaCapture mediaCapture) {
    if (mediaCapture.status == MediaCaptureStatus.success) {
      return mediaCapture.captureRequest.when(
        single: (single) => single.file!.path,
        multiple: (multiple) => multiple.fileBySensor.values.first!.path,
      );
    } else {
      return "null found";
    }
  }

  static Future<Uint8List?> showAIPickerSheet(BuildContext context) async {
    Map<String, Object>? image;

    image = await CustomSnackBar.showCustomBottomSheet<Map<String, Object>?>(
      color: context.scaffoldBackgroundColor,
      showDivider: false,
      maxHeight: 160,
      maxWidth: 500,
      bottomSheet: _buildCustomCamera(),
    );
    if (image != null) {
      GlobalHelper.showPopupLoader();
      final fileData = await (image['fileData'] as XFile).readAsBytes();
      final rectData = (image['rectData'] as double);
      final cardType = (image['cardType'] as String);
      final cropbytes = ImagePickerService.cropImage(
        fileData,
        rectData,
        Get.context!.deviceWidth,
        cardType,
      );
      return cropbytes;
    }
    return null;
  }

  static Future<Uint8List?> openFrame(String cardtype) async {
    Map<String, Object>? image;
    image = await pickCustomAIImage(cardtype);
    if (image != null) {
      GlobalHelper.showPopupLoader();
      final fileData = await (image['fileData'] as XFile).readAsBytes();
      final rectData = (image['rectData'] as double);
      final cardType = (image['cardType'] as String);
      final cropbytes = ImagePickerService.cropImage(fileData, rectData, Get.context!.deviceWidth, cardType);
      return cropbytes;
    }
    return null;
  }

  static Widget _buildCustomCamera() {
    Map<String, Object>? image;
    return Material(
      type: MaterialType.transparency,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            onTap: () async {
              image = await pickCustomAIImage('national');
              Get.back<Map<String, Object>>(result: image);
            },
            leading: const Icon(
              Icons.camera_alt_outlined,
              color: AppColors.primary,
            ),
            title: Text(Strings.FROM_CARD, style: Get.context!.bodyLarge),
          ),
          ListTile(
            onTap: () async {
              image = await pickCustomAIImage('passport');
              Get.back<Map<String, Object>>(result: image);
            },
            leading: const Icon(Icons.camera_alt_outlined, color: AppColors.primary),
            title: Text(Strings.FROM_PASSPORT, style: Get.context!.bodyLarge),
          ),
        ],
      ),
    );
  }

  static Future<Map<String, Object>?> pickCustomAIImage(cardType) async {
    final Completer<Map<String, Object>?> completer = Completer<Map<String, Object>?>();
    Get.to(
      CameraAwesomeBuilder.awesome(
        saveConfig: SaveConfig.photo(),
        sensorConfig: SensorConfig.single(
          aspectRatio: CameraAspectRatios.ratio_16_9,
          flashMode: FlashMode.none,
          sensor: Sensor.position(SensorPosition.back),
          zoom: 0.0,
        ),
        previewDecoratorBuilder: (state, preview) {
          state.captureState$.listen((MediaCapture? mediaCapture) async {
            if (mediaCapture != null) {
              switch (mediaCapture.status) {
                case MediaCaptureStatus.success:
                  final filePath = ImagePickerService.filePath(mediaCapture);
                  completer.complete({'fileData': XFile(filePath), 'rectData': preview.previewSize.aspectRatio, 'cardType': cardType});
                  Get.back();
                  break;
                case MediaCaptureStatus.capturing:
                  break;
                case MediaCaptureStatus.failure:
                  completer.complete(null);
                  break;
              }
            }
          });
          return MRZCameraOverlay(documentType: cardType, child: Container());
        },
        topActionsBuilder: (state) => Container(),
        middleContentBuilder: (state) => Container(),
        bottomActionsBuilder: (state) => AwesomeBottomActions(
          state: state,
          left: Container(),
          right: Container(),
        ),
      ),
    );

    return completer.future;
  }

  static Future<Uint8List> cropImage(Uint8List imageBytes, double cropRect, double width, String cardType) async {
    return await compute(_cropImage, [imageBytes, cropRect, width, cardType]);
  }

  static Uint8List _cropImage(List<dynamic> args) {
    final decodedImage = img.decodeImage(args[0]);
    if (decodedImage == null) {
      throw Exception('Failed to decode image');
    }
    final cameraWidth = args[2];
    final cameraHeight = args[2] * args[1];
    final rectangleX = cameraWidth * 0.02;
    final rectangleY = cameraHeight * 0.35;
    final rectangleWidth = cameraWidth * 0.8;
    final rectangleHeight = cameraHeight * 0.3;
    final imageWidth = decodedImage.width;
    final imageHeight = decodedImage.height;
    final cropX = (rectangleX * imageWidth) / cameraWidth;
    final cropY = (rectangleY * imageHeight) / cameraHeight;
    final cropWidth1 = (rectangleWidth * imageWidth) / cameraWidth;
    final cropHeight1 = (rectangleHeight * imageHeight) / cameraHeight;
    final cropWidth = cropWidth1;
    final cropHeight = cropHeight1;
    final offsetX = cropX;
    final offsetY = cropY;
    final croppedImage = img.copyCrop(decodedImage, x: offsetX.toInt(), y: offsetY.toInt(), width: cropWidth.toInt(), height: cropHeight.toInt());
    List<int> intImage = img.encodeJpg(croppedImage);
    Uint8List myUint8List = Uint8List.fromList(intImage);
    return myUint8List;
  }
}
