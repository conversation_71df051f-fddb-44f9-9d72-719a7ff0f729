import 'package:digital_onboarding/digital_onboarding.dart';

import '../../../resources/exports/index.dart';

enum DocType { front, back }

enum DocumentTypeSelection { nationalId, passport }

enum VerificationStep { documentSelection, frontInformational, frontCapture, frontConfirmation, backInformational, backCapture, backConfirmation, completed }

class DocVerificationController extends GetxController {
  Uint8List? image;
  DocType docType = DocType.front;
  VerifyDocModel? doc;
  DocumentTypeSelection? selectedDocumentType;
  VerificationStep currentStep = VerificationStep.documentSelection;

  Uint8List? doc1;
  Uint8List? doc2;

  // Add reactive variables for UI updates
  RxBool isLoading = false.obs;
  RxString currentCardType = ''.obs;

  // Document type selection
  void selectDocumentType(DocumentTypeSelection type) {
    selectedDocumentType = type;
    currentCardType.value = type == DocumentTypeSelection.nationalId ? 'national' : 'passport';
    update(['document_selection']);
  }

  // Start verification flow
  void startVerification() {
    if (selectedDocumentType == null) return;

    currentStep = VerificationStep.frontInformational;
    docType = DocType.front;
    update(['verification_step']);
  }

  // Open camera for document capture
  Future<void> openCamera() async {
    try {
      String cardType = selectedDocumentType == DocumentTypeSelection.nationalId ? 'national' : 'passport';

      image = await ImagePickerService.openFrame(cardType);

      if (image != null) {
        currentStep = docType == DocType.front ? VerificationStep.frontConfirmation : VerificationStep.backConfirmation;
        update(['doc_picture', 'verification_step']);
      }
    } catch (e) {
      CustomSnackBar.errorSnackBar(message: "Failed to capture image: $e");
    }
  }

  // Show confirmation dialog and verify document
  Future<void> confirmAndVerify() async {
    if (image == null) {
      CustomSnackBar.errorSnackBar(message: Strings.IMAGE_FIRST);
      return;
    }

    isLoading.value = true;

    try {
      final XFile file = await GlobalHelper().getFileFromUint8List(image!, "doc");

      log.i("Starting document verification - ${docType == DocType.front ? 'FRONT' : 'BACK'} side");

      final VerifyDocModel? model = await DigitalOnboardingServices.submitDocument(
        file,
        step: docType == DocType.front ? StepEnum.documentFront : StepEnum.documentBack,
      );

      isLoading.value = false;

      if (model?.verified ?? false) {
        if (model?.isBackRequired ?? false && docType == DocType.front) {
          // Front side verified, now need back side
          _proceedToBackSide();
        } else {
          // Verification complete
          _completeVerification();
        }
      } else {
        CustomSnackBar.errorSnackBar(message: "Document verification failed. Please try again.");
      }
    } on DigitalOnboardingException catch (e) {
      isLoading.value = false;
      CustomSnackBar.errorSnackBar(message: e.message ?? Strings.SOMETHING_WENT_WRONG);
    } catch (e) {
      isLoading.value = false;
      CustomSnackBar.errorSnackBar(message: "Unexpected error: $e");
    }
  }

  // Proceed to back side verification
  void _proceedToBackSide() {
    docType = DocType.back;
    image = null;
    currentStep = VerificationStep.backInformational;
    update(['verification_step', 'doc_picture']);
    CustomSnackBar.successSnackBar(message: "Front side verified! Now scan the back side.");
  }

  // Complete verification
  void _completeVerification() {
    currentStep = VerificationStep.completed;
    update(['verification_step']);
    CustomSnackBar.successSnackBar(message: "Document verification completed!");

    // Navigate back to dashboard
    Future.delayed(const Duration(seconds: 1), () {
      Get.offAllNamed(Routes.DASHBOARD);
    });
  }

  // Retry capture
  void retryCapture() {
    image = null;
    currentStep = docType == DocType.front ? VerificationStep.frontCapture : VerificationStep.backCapture;
    update(['doc_picture', 'verification_step']);
  }

  Future<void> updateImage() async {
    update(['doc_picture']);
  }

  void resetVerification() {
    image = null;
    docType = DocType.front;
    doc = null;
    selectedDocumentType = null;
    currentStep = VerificationStep.documentSelection;
    doc1 = null;
    doc2 = null;
    isLoading.value = false;
    currentCardType.value = '';
    update(['verification_step', 'doc_picture', 'document_selection']);
  }

  // Helper methods
  String get currentStepTitle {
    switch (currentStep) {
      case VerificationStep.documentSelection:
        return "Select Document Type";
      case VerificationStep.frontInformational:
        return "Scan Document Front";
      case VerificationStep.frontCapture:
        return "Capture Front Side";
      case VerificationStep.frontConfirmation:
        return "Confirm Front Side";
      case VerificationStep.backInformational:
        return "Scan Document Back";
      case VerificationStep.backCapture:
        return "Capture Back Side";
      case VerificationStep.backConfirmation:
        return "Confirm Back Side";
      case VerificationStep.completed:
        return "Verification Complete";
    }
  }

  bool get isDocumentTypeSelected => selectedDocumentType != null;

  bool get canProceed {
    switch (currentStep) {
      case VerificationStep.documentSelection:
        return selectedDocumentType != null;
      case VerificationStep.frontConfirmation:
      case VerificationStep.backConfirmation:
        return image != null;
      default:
        return true;
    }
  }

  @override
  void onInit() {
    resetVerification();
    super.onInit();
  }

  @override
  void dispose() {
    doc1 = null;
    doc2 = null;
    super.dispose();
  }
}
