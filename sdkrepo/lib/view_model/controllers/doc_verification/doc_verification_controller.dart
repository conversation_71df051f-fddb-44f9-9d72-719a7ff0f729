import 'package:digital_onboarding/digital_onboarding.dart';

import '../../../resources/exports/index.dart';

enum DocType { front, back }

enum DocumentTypeSelection { nationalId, passport }

enum VerificationStep { documentSelection, frontInformational, frontCapture, frontConfirmation, backInformational, backCapture, backConfirmation, completed }

class DocVerificationController extends GetxController {
  Uint8List? image;
  DocType docType = DocType.front;
  VerifyDocModel? doc;
  DocumentTypeSelection? selectedDocumentType;
  VerificationStep currentStep = VerificationStep.documentSelection;

  Uint8List? doc1;
  Uint8List? doc2;

  // Document type selection methods
  void selectDocumentType(DocumentTypeSelection type) {
    selectedDocumentType = type;
    currentStep = VerificationStep.frontInformational;
    update(['verification_step']);
  }

  void proceedToFrontCapture() {
    currentStep = VerificationStep.frontCapture;
    update(['verification_step']);
  }

  void proceedToBackInformational() {
    currentStep = VerificationStep.backInformational;
    update(['verification_step']);
  }

  void proceedToBackCapture() {
    currentStep = VerificationStep.backCapture;
    update(['verification_step']);
  }

  // Camera capture methods
  void captureDocument() async {
    String cardType = selectedDocumentType == DocumentTypeSelection.nationalId ? 'national' : 'passport';

    // Use the appropriate camera capture method
    image = await ImagePickerService.openFrame(cardType);

    if (image != null) {
      currentStep = docType == DocType.front ? VerificationStep.frontConfirmation : VerificationStep.backConfirmation;
      await updateImage();
    }
  }

  void retryCapture() {
    image = null;
    currentStep = docType == DocType.front ? VerificationStep.frontCapture : VerificationStep.backCapture;
    updateImage();
  }

  void saveAndVerifyDocument() async {
    await verify();
  }

  void pickImage() async {
    if (docType == DocType.front) {
      image = await ImagePickerService.showAIPickerSheet(Get.context!);
    } else {
      image = await ImagePickerService.openFrame('national');
    }
    Get.back();
    await updateImage();
  }

  Future<void> updateImage() async {
    update(['doc_picture']);
  }

  Future<void> verify() async {
    if (image == null) {
      CustomSnackBar.errorSnackBar(message: Strings.IMAGE_FIRST);
      return;
    }

    try {
      final XFile file = await GlobalHelper().getFileFromUint8List(image!, "doc");
      final VerifyDocModel? model = await DigitalOnboardingServices.submitDocument(
        file,
        step: docType == DocType.front ? StepEnum.documentFront : StepEnum.documentBack,
      );

      if (model?.verified ?? false) {
        if (model?.isBackRequired ?? false && docType == DocType.front) {
          // Front side verified, now need back side
          docType = DocType.back;
          image = null;
          doc = model; // Store the model for later reference
          currentStep = VerificationStep.backInformational;
          updateImage();
          CustomSnackBar.successSnackBar(message: "Front side verified! Now scan the back side.");
          return;
        } else {
          // Both sides verified or only front side required
          currentStep = VerificationStep.completed;
          CustomSnackBar.successSnackBar(message: "Document verification completed!");
        }
      } else {
        return CustomSnackBar.errorSnackBar(message: "Document verification failed. Please try again.");
      }

      // Navigate back to dashboard after successful completion
      Get.back();
      Get.offAllNamed(Routes.DASHBOARD);
    } on DigitalOnboardingException catch (e) {
      CustomSnackBar.errorSnackBar(message: Strings.SOMETHING_WENT_WRONG);
    }
  }

  Future<void> onUpdate() async {
    if (doc1 == null) {
      CustomSnackBar.errorSnackBar(message: Strings.IMAGE_FIRST);
      return;
    }

    if (doc1 != null) {
      try {
        XFile file = await GlobalHelper().getFileFromUint8List(doc1!, "Document1");
        await DigitalOnboardingServices.submitDocument(file, step: StepEnum.documentFront);
        Get.offAllNamed(Routes.DASHBOARD);
      } on DigitalOnboardingException catch (e) {}
    }

    if (doc2 != null) {
      try {
        XFile file = await GlobalHelper().getFileFromUint8List(doc2!, "Document2");
        await DigitalOnboardingServices.submitDocument(file, step: StepEnum.documentFront);
        Get.offAllNamed(Routes.DASHBOARD);
      } on DigitalOnboardingException catch (e) {}
    }
  }

  void resetVerification() {
    image = null;
    docType = DocType.front;
    doc = null;
    selectedDocumentType = null;
    currentStep = VerificationStep.documentSelection;
    doc1 = null;
    doc2 = null;
    update(['verification_step', 'doc_picture']);
  }

  @override
  void onInit() {
    resetVerification();
    super.onInit();
  }

  @override
  void dispose() {
    doc1 = null;
    doc2 = null;
    super.dispose();
  }
}
